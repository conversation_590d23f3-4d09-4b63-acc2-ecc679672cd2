# OGCoordinator

How to use the coordinator SDK.

## Obtain the `OGCoordinator` instance

```kotlin
// Ko<PERSON>in
val ogCoordinator = OGAppKitSdk.init(androidApplication()).coordinator()
```

```swift
// Swift
let ogCoordinator = OGAppKitSdk.shared.coordinator()
```

## Configure it

```kotlin
ogCoordinator.configure(
    CoordinatorConfig(
        debounceMs = 500,
        behaviors = listOf(
            Behavior(
                id = "push_notification_prompt",
                precondition = Precondition.PUSH_DISABLED,
                conditions = listOf(
                    Condition.AppStarts(start = 3, period = 5),
                    Condition.ScreenViews(start = 10, period = 15)
                ),
                action = Action.Navigation(url = "app://settings/notifications"),
                maxInvocations = 3,
                minutesBetweenInvocations = 1440 // 24 hours
            )
        ),
        pushOptInGranted = false
    )
)
```

Where:

- `debounceMs` is the debounce interval in milliseconds after which the highest-priority behavior is triggered. Defaults to 500ms.
- `behaviors` is a list of behaviors that define when and what actions should be performed based on app events and conditions.
- `pushOptInGranted` indicates whether push notification opt-in has been granted. Used to evaluate push-related preconditions.

## Push Permission Delegate (iOS Recommended)

For iOS apps, you can implement automatic push permission detection using a delegate instead of manually providing `pushOptInGranted`:

```swift
import UserNotifications
import OGAppKitSDK

// 1. Implement the delegate
class IOSPushPermissionDelegate: PushPermissionDelegate {
    func isPushOptInGranted() async -> Bool {
        let center = UNUserNotificationCenter.current()
        let settings = await center.notificationSettings()

        switch settings.authorizationStatus {
        case .authorized, .provisional, .ephemeral:
            return true
        case .denied, .notDetermined:
            return false
        @unknown default:
            return false
        }
    }
}

// 2. Set the delegate
let coordinator = OGAppKitSDK.shared.coordinator
let pushDelegate = IOSPushPermissionDelegate()
coordinator.setPushPermissionDelegate(delegate: pushDelegate)

// 3. Configure without pushOptInGranted parameter
coordinator.configure(debounceMs: 500, behaviorsJson: behaviorsJson)
```

**Benefits of using a delegate:**
- Automatic permission detection on iOS
- No need to manually track permission changes
- Cleaner API - no need to pass `pushOptInGranted`
- Real-time permission status updates

## Handle Events and Actions

The Coordinator SDK works by processing events and triggering actions based on configured behaviors. Events represent what happens in the app, while actions represent what should be done in response.

### Send Events

The AppStart event and all the tracking events will be automatically processed by the SDK after the first configuration. The other events can be sent manually from the client apps like this:

```kotlin
// Kotlin
// Screen navigation
ogCoordinator.onEvent(CoordinatorEvent.ScreenView(url = "https://shop.de/products"))

// Web bridge calls
ogCoordinator.onEvent(CoordinatorEvent.WebBridgeCall(name = "showRatingPrompt"))
```

```swift
// Swift
// Screen navigation
ogCoordinator.onEvent(CoordinatorEvent.ScreenView(url: "https://shop.de/products"))

// Web bridge calls
ogCoordinator.onEvent(CoordinatorEvent.WebBridgeCall(name: "showRatingPrompt"))
```

### Listen for Actions

Actions are emitted as a Flow/AsyncSequence that you can observe to respond to coordinator decisions:

```kotlin
// Kotlin
ogCoordinator.getActions().collect { action ->
    when (action) {
        is Action.Navigation -> {
            // Navigate to the specified URL
            navigateTo(action.url)
        }
    }
}
```

```swift
// Swift
for await action in ogCoordinator.getActions() {
    switch onEnum(of: action) {
    case .navigation(let navigationAction):
        // Navigate to the specified URL
        navigateTo(url: navigationAction.url)
    }
}
```

## Behaviors

Behaviors are the core concept of the Coordinator SDK. They define rules for when certain actions should be triggered based on app events and conditions.

### Behavior Structure

A behavior consists of:

- **ID**: A unique identifier for the behavior
- **Precondition**: Optional condition that must be met before evaluating other conditions
- **Conditions**: List of conditions that trigger the action (ANY condition being met will trigger)
- **Action**: The action to perform when conditions are met
- **Max Invocations**: Optional limit on how many times this behavior can be triggered
- **Minutes Between Invocations**: Optional cooldown period between invocations
- **Disabled URLs**: List of URL patterns where this behavior should be disabled

### Preconditions

Preconditions must be fulfilled before any conditions are evaluated:

- `PUSH_ENABLED`: Push notifications are enabled
- `PUSH_DISABLED`: Push notifications are disabled

### Conditions

Conditions define when a behavior should trigger:

#### App Starts

Triggers based on the number of app starts:

```kotlin
Condition.AppStarts(
    start = 3,  // Start checking after 3rd app start
    period = 5  // Trigger every 5th app start after that
)
```

#### Screen Views

Triggers based on the number of screen views:

```kotlin
Condition.ScreenViews(
    start = 10,  // Start checking after 10th screen view
    period = 15  // Trigger every 15th screen view after that
)
```

#### Web Bridge Calls

Triggers when a specific web bridge call is made:

```kotlin
Condition.WebBridgeCall(
    webBridgeCallName = "showRatingPrompt"
)
```

#### Tracking Events

Triggers based on tracking events that occur within the app. Tracking events are automatically processed by the Coordinator SDK and stored persistently every 5 seconds to the filesystem for durability across app sessions.

```kotlin
Condition.TrackingEvents(
    eventName = "AccountLogin",
    countType = CountType.TOTAL,  // or CountType.SESSION
    start = 1,   // Start checking after 1st occurrence
    period = 3   // Trigger every 3rd occurrence after that
)
```

**Parameters:**
- `eventName`: The class name of the tracking event to monitor (see available event types below)
- `countType`: Whether to count occurrences per session (`SESSION`) or across all app sessions (`TOTAL`)
- `start`: The number of occurrences after which to start checking the condition
- `period`: How often to trigger after the start threshold is reached

**Automatic Processing:**
- Tracking events are automatically collected by the Coordinator SDK from the tracking system
- Events are stored in memory and flushed to persistent storage every 5 seconds
- Session counts are reset when the app is restarted, while total counts persist across sessions
- The system handles app backgrounding by immediately flushing pending events to storage

**Available Event Types:**

*Interaction Events:*
- `AccountLogin` - User taps login button on Account screen
- `AccountMenuEntry` - User selects menu entry on Account screen
- `HomeBottomNavigationEntry` - User taps Home in bottom navigation
- `AssortmentBottomNavigationEntry` - User taps Assortment in bottom navigation
- `WishlistBottomNavigationEntry` - User taps Wishlist in bottom navigation
- `CartBottomNavigationEntry` - User taps Cart in bottom navigation
- `AccountBottomNavigationEntry` - User taps Account in bottom navigation
- `Back` - User taps back button
- `ShareProduct` - User shares a product
- `LoggedIn` - User successfully logs in
- `LoggedOut` - User logs out
- `AddToWishlist` - User adds item to wishlist
- `AddToCart` - User adds item to cart
- `PPLConfirmOnboarding` - User confirms push permission prompt during onboarding
- `PPLCloseOnboarding` - User closes push permission prompt during onboarding
- `PPLConfirmScreenViews` - User confirms push permission prompt during screen views
- `PPLCloseScreenViews` - User closes push permission prompt during screen views
- `PPLConfirmOrderConfirmation` - User confirms push permission prompt on order confirmation
- `PPLCloseOrderConfirmation` - User closes push permission prompt on order confirmation
- `PPLConfirmWebBridge` - User confirms push permission prompt triggered by web bridge
- `PPLCloseWebBridge` - User closes push permission prompt triggered by web bridge
- `PPLConfirmInbox` - User confirms push permission prompt in inbox
- `PPLCloseInbox` - User closes push permission prompt in inbox
- `PushSystemConfirmInbox` - User confirms system push permission prompt in inbox
- `PushSystemCloseInbox` - User closes system push permission prompt in inbox
- `PushSystemConfirmOnboarding` - User confirms system push permission prompt during onboarding

*View Events (Screen Navigation):*
- `AccountMenu` - Account menu screen viewed
- `CountryScreen` - Country chooser screen viewed
- `BraFittingGuideStep` - Bra fitting guide step viewed
- `CatalogScannerIntro` - Catalog scanner intro screen viewed
- `CatalogScannerScan` - Catalog scanner scan screen viewed
- `CategoryMenu` - Category menu screen viewed
- `DealsOnboarding` - Deals onboarding screen viewed
- `DealsOnboardingVideo` - Deals onboarding video screen viewed
- `DealsOverview` - Deals overview screen viewed
- `Error` - Error screen viewed
- `InAppInboxMessage` - In-app inbox message screen viewed
- `InAppInboxOverview` - In-app inbox overview screen viewed
- `Search` - Search screen viewed
- `StorefinderStoreOverviewList` - Store finder list screen viewed
- `StorefinderStoreOverviewMap` - Store finder map screen viewed
- `StorefinderStoreDetail` - Store detail screen viewed
- `Welcome` - Welcome/onboarding screen viewed
- `Basket` - Cart/basket screen viewed
- `Checkout` - Checkout screen viewed
- `Home` - Home screen viewed
- `Login` - Login screen viewed

**Example Usage:**

```kotlin
// Trigger after user has logged in 3 times total
Condition.TrackingEvents(
    eventName = "LoggedIn",
    countType = CountType.TOTAL,
    start = 3,
    period = 1
)

// Trigger every 5th time user adds item to cart in current session
Condition.TrackingEvents(
    eventName = "AddToCart",
    countType = CountType.SESSION,
    start = 5,
    period = 5
)

// Trigger after user views home screen 10 times across all sessions
Condition.TrackingEvents(
    eventName = "Home",
    countType = CountType.TOTAL,
    start = 10,
    period = 1
)
```

### Actions

Currently supported actions:

#### Navigation

Navigate to a specific URL:

```kotlin
Action.Navigation(url = "app://settings/notifications")
```

## Example Use Cases

### Push Notification Prompt

Show a push notification prompt after the user has started the app 3 times and is not on certain screens:

```kotlin
Behavior(
    id = "push_prompt",
    precondition = Precondition.PUSH_DISABLED,
    conditions = listOf(Condition.AppStarts(start = 3, period = 1)),
    action = Action.Navigation(url = "app://permissions/push"),
    maxInvocations = 1,
    disabledUrls = listOf(
        UrlMatcher(".*checkout.*"),
        UrlMatcher(".*payment.*")
    )
)
```

### Rating Prompt

Show a rating prompt after certain screen views or when triggered by web content:

```kotlin
Behavior(
    id = "rating_prompt",
    conditions = listOf(
        Condition.ScreenViews(start = 20, period = 50),
        Condition.WebBridgeCall(webBridgeCallName = "showRatingPrompt")
    ),
    action = Action.Navigation(url = "app://rating"),
    maxInvocations = 3,
    minutesBetweenInvocations = 10080 // 1 week
)
```

### User Engagement Based on Login Activity

Trigger special offers after users have logged in multiple times:

```kotlin
Behavior(
    id = "frequent_user_offer",
    conditions = listOf(
        Condition.TrackingEvents(
            eventName = "LoggedIn",
            countType = CountType.TOTAL,
            start = 5,
            period = 10
        )
    ),
    action = Action.Navigation(url = "app://offers/loyal-customer"),
    maxInvocations = 5,
    minutesBetweenInvocations = 4320 // 3 days
)
```

### Cart Abandonment Prompt

Show a reminder after users add items to cart but don't complete checkout:

```kotlin
Behavior(
    id = "cart_reminder",
    conditions = listOf(
        Condition.TrackingEvents(
            eventName = "AddToCart",
            countType = CountType.SESSION,
            start = 2,
            period = 1
        )
    ),
    action = Action.Navigation(url = "app://cart/reminder"),
    maxInvocations = 2,
    minutesBetweenInvocations = 60, // 1 hour
    disabledUrls = listOf(
        UrlMatcher(".*checkout.*"),
        UrlMatcher(".*cart.*")
    )
)
```
