package com.ottogroup.appkit.coordinator.platform

import com.ottogroup.appkit.coordinator.PushPermissionDelegate

/**
 * iOS implementation of PushPermissionProvider.
 * Uses a delegate pattern to allow Swift-side implementation of push permission checking.
 * If no delegate is set, falls back to the configured value.
 */
internal actual class PushPermissionProvider {
    private var delegate: PushPermissionDelegate? = null

    actual fun setDelegate(delegate: PushPermissionDelegate?) {
        this.delegate = delegate
    }

    actual suspend fun isPushOptInGranted(configuredValue: Boolean): Bo<PERSON>an {
        // If delegate is set, use it (this is the preferred approach on iOS)
        delegate?.let { return it.isPushOptInGranted() }

        // Otherwise, fall back to the configured value
        return configuredValue
    }
}
