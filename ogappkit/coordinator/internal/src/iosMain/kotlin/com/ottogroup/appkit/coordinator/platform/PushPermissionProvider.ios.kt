package com.ottogroup.appkit.coordinator.platform

import com.ottogroup.appkit.coordinator.PushPermissionDelegate

/**
 * iOS implementation of PushPermissionProvider.
 * Uses constructor-injected delegate for push permission checking.
 */
internal actual class PushPermissionProvider actual constructor(private val delegate: PushPermissionDelegate) {

    actual suspend fun getPushOptInStatus(configuredValue: <PERSON><PERSON><PERSON>): <PERSON><PERSON><PERSON> {
        // Always use the injected delegate on iOS
        return delegate.isPushOptInGranted()
    }
}


