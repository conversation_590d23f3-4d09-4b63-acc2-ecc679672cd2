package com.ottogroup.appkit.coordinator.platform

import com.ottogroup.appkit.coordinator.PushPermissionDelegate

/**
 * Android implementation of PushPermissionProvider.
 * This is just a stub since Android uses the configured value directly in OGCoordinatorImpl.
 * The provider is only created when setPushPermissionDelegate is called, but on Android
 * the precondition checking will still use the configured value directly.
 */
internal actual class PushPermissionProvider actual constructor(private val delegate: PushPermissionDelegate) {
    
    actual suspend fun getPushOptInStatus(configuredValue: Boolean): Bo<PERSON>an {
        // This should never be called on Android since the provider is only created
        // when setPushPermissionDelegate is called, but Android uses configured value directly
        return configuredValue
    }
}
