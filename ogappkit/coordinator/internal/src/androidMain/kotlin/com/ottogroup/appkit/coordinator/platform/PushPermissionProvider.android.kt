package com.ottogroup.appkit.coordinator.platform

import com.ottogroup.appkit.coordinator.PushPermissionDelegate

/**
 * Android implementation of PushPermissionProvider.
 * Uses the manually configured value since Android apps need to manage
 * push permission state explicitly. Delegate support is available but
 * typically not needed on Android.
 */
internal actual class PushPermissionProvider {
    private var delegate: PushPermissionDelegate? = null

    actual fun setDelegate(delegate: PushPermissionDelegate?) {
        this.delegate = delegate
    }

    actual suspend fun isPushOptInGranted(configuredValue: Boolean): Bo<PERSON>an {
        // If delegate is set, use it
        delegate?.let { return it.isPushOptInGranted() }

        // Otherwise, use the configured value as provided by the app
        return configuredValue
    }
}
