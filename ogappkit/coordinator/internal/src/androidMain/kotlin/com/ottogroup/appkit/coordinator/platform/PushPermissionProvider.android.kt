package com.ottogroup.appkit.coordinator.platform

import com.ottogroup.appkit.coordinator.PushPermissionDelegate

/**
 * Android implementation of PushPermissionProvider.
 * Always uses the configured value directly - simple and efficient.
 * Delegate is ignored on Android since manual permission management is preferred.
 */
internal actual class PushPermissionProvider {

    actual fun setDelegate(delegate: PushPermissionDelegate?) {
        // Ignored on Android - we always use the configured value
    }

    actual suspend fun getPushOptInStatus(configuredValue: Boolean): Bo<PERSON>an {
        // On Android, always use the configured value directly
        return configuredValue
    }
}
