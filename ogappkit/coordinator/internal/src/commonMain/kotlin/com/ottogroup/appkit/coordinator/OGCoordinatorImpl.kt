package com.ottogroup.appkit.coordinator

import com.ottogroup.appkit.base.lifecycle.ApplicationLifecycleProvider
import com.ottogroup.appkit.base.lifecycle.LifecycleEvent
import com.ottogroup.appkit.base.uri.anyMatch
import com.ottogroup.appkit.base.uri.sanitize
import com.ottogroup.appkit.coordinator.config.CoordinatorConfig
import com.ottogroup.appkit.coordinator.config.CoordinatorConfigProvider
import com.ottogroup.appkit.coordinator.data.TrackingEventsRepository
import com.ottogroup.appkit.coordinator.model.Action
import com.ottogroup.appkit.coordinator.model.Behavior
import com.ottogroup.appkit.coordinator.model.Condition
import com.ottogroup.appkit.coordinator.model.CoordinatorEvent
import com.ottogroup.appkit.coordinator.model.Precondition
import com.ottogroup.appkit.coordinator.platform.PushPermissionProvider
import com.ottogroup.appkit.coordinator.utils.CoordinatorLogger
import com.ottogroup.appkit.tracking.OGTrackingDealer
import com.ottogroup.appkit.tracking.collectEvents
import com.ottogroup.appkit.tracking.event.GenericEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.json.Json

internal class OGCoordinatorImpl(
    private val configProvider: CoordinatorConfigProvider,
    private val appStartCounter: AppStartCounter,
    private val screenViewCounter: ScreenViewCounter,
    private val behaviorQueue: BehaviorQueue,
    private val coroutineScope: CoroutineScope,
    private val trackingDealer: OGTrackingDealer,
    private val trackingEventsRepository: TrackingEventsRepository,
    private val applicationLifecycleProvider: ApplicationLifecycleProvider
) : OGCoordinator {
    private val pushPermissionProvider: PushPermissionProvider? = createPushPermissionProviderIfNeeded()
    private val behaviors: List<Behavior> get() = configProvider.configState.value.behaviors
    private var currentScreenViewUrl: String? = null
    private var appStartSent = false

    override fun configure(config: CoordinatorConfig) {
        configProvider.update(config)
        // Send AppStart event and observe tracking events exactly once after first configuration
        if (!appStartSent) {
            onEvent(CoordinatorEvent.AppStart)
            observeTrackingEvents()
            observeApplicationLifecycle()
            appStartSent = true
        }
    }

    override fun configure(debounceMs: Long, behaviorsJson: String, pushOptInGranted: Boolean) {
        try {
            val behaviors = Json.decodeFromString<List<Behavior>>(behaviorsJson)
            val config = CoordinatorConfig(
                debounceMs = debounceMs,
                behaviors = behaviors,
                pushOptInGranted = pushOptInGranted
            )
            configure(config)
        } catch (e: Exception) {
            CoordinatorLogger.e("coordinator: Failed to parse JSON behaviors: $e")
            // Fallback to empty configuration
            configure(CoordinatorConfig(debounceMs = debounceMs, behaviors = emptyList(), pushOptInGranted = pushOptInGranted))
        }
    }

    private fun observeTrackingEvents() {
        trackingDealer.collectEvents { event ->
            if (event is GenericEvent) return@collectEvents
            onEvent(CoordinatorEvent.TrackingEvent(event))
        }
    }

    private fun observeApplicationLifecycle() {
        coroutineScope.launch {
            applicationLifecycleProvider.lifecycle.collect { lifecycleEvent ->
                when (lifecycleEvent) {
                    is LifecycleEvent.AppInBackground -> {
                        CoordinatorLogger.d("coordinator: app went to background, flushing events")
                        trackingEventsRepository.onAppBackground()
                    }

                    is LifecycleEvent.AppInForeground -> {
                        CoordinatorLogger.d("coordinator: app returned to foreground, resuming event tracking")
                    }
                }
            }
        }
    }

    override fun onEvent(event: CoordinatorEvent) {
        coroutineScope.launch {
            CoordinatorLogger.d("coordinator: event received: $event")
            when (event) {
                CoordinatorEvent.AppStart -> onAppStart()
                is CoordinatorEvent.ScreenView -> onScreenView(event.url)
                is CoordinatorEvent.WebBridgeCall -> onWebBridgeCall(event.name)
                is CoordinatorEvent.TrackingEvent -> onTrackingEvent(event)
            }
        }
    }

    private suspend fun onAppStart() {
        val appStartsCount = appStartCounter.incrementCount()
        CoordinatorLogger.d("coordinator: onAppStart $appStartsCount")
        behaviors.withPreconditionsFulfilled().forEachIndexed { index, behavior ->
            val conditions = behavior.conditions.filterIsInstance<Condition.AppStarts>()
            if (conditions.any { it.isMet(appStartsCount) }) {
                behavior.enqueueForExecution(index)
            }
        }
    }

    private suspend fun onScreenView(url: String?) {
        val sanitizedUrl = url?.sanitize()
        if (sanitizedUrl != null && sanitizedUrl == currentScreenViewUrl) return
        currentScreenViewUrl = sanitizedUrl

        behaviors
            .withPreconditionsFulfilled()
            .withUrlFulfilled(sanitizedUrl)
            .forEachIndexed { index, behavior ->
                val screenViewCount = screenViewCounter.incrementCount(behavior.id)
                val conditions = behavior.conditions.filterIsInstance<Condition.ScreenViews>()
                conditions.forEach { condition ->
                    CoordinatorLogger.d("coordinator: onScreenView $screenViewCount for behavior ${behavior.id} - $sanitizedUrl")
                    if (condition.isMet(screenViewCount)) {
                        behavior.enqueueForExecution(index)
                    }
                }
            }
    }

    private suspend fun onWebBridgeCall(callName: String) {
        CoordinatorLogger.d("coordinator: onWebBridgeCall $callName")
        behaviors.withPreconditionsFulfilled().forEachIndexed { index, behavior ->
            val conditions = behavior.conditions.filterIsInstance<Condition.WebBridgeCall>()
            if (conditions.any { it.isMet(callName) }) {
                behavior.enqueueForExecution(index)
            }
        }
    }

    private suspend fun onTrackingEvent(event: CoordinatorEvent.TrackingEvent) {
        trackingEventsRepository.addSessionEvent(event)
        val sessionCount = trackingEventsRepository.getSessionOccurrences(event)
        val totalCount = trackingEventsRepository.getTotalOccurrences(event)
        CoordinatorLogger.d(
            "coordinator: tracking event $event - session count: $sessionCount, total count: $totalCount"
        )

        behaviors.withPreconditionsFulfilled().forEachIndexed { index, behavior ->
            val conditions = behavior.conditions.filterIsInstance<Condition.TrackingEvents>()
            conditions.forEach { condition ->
                if (condition.isMet(event, sessionCount, totalCount)) {
                    behavior.enqueueForExecution(index)
                }
            }
        }
    }

    private suspend fun Behavior.enqueueForExecution(priority: Int) {
        currentScreenViewUrl?.let { url ->
            if (this.disabledUrls.anyMatch(url)) {
                CoordinatorLogger.d("coordinator: skipped enqueuing behavior ${this.id} because it excludes current URL $url")
                return
            }
        }

        behaviorQueue.enqueue(this, priority)
    }

    private suspend fun List<Behavior>.withPreconditionsFulfilled(): List<Behavior> {
        val (fulfilled, unfulfilled) = partition { it.precondition.isFulfilled() }
        if (unfulfilled.isNotEmpty()) {
            CoordinatorLogger.d("coordinator: ignored behaviors not fulfilling precondition: ${unfulfilled.map { it.id }}")
        }
        return fulfilled
    }

    private fun List<Behavior>.withUrlFulfilled(url: String?): List<Behavior> {
        if (url == null) return this

        val (excluded, notExcluded) = partition { it.disabledUrls.anyMatch(url) }
        if (excluded.isNotEmpty()) {
            CoordinatorLogger.d("coordinator: ignored behaviors that exclude URL $url: ${excluded.map { it.id }}")
        }
        return notExcluded
    }

    private suspend fun Precondition?.isFulfilled(): Boolean {
        if (this == null) return true

        val configuredValue = configProvider.configState.value.pushOptInGranted ?: false
        val pushOptInGranted = pushPermissionProvider?.getPushOptInStatus(configuredValue) ?: configuredValue

        return when (this) {
            Precondition.PUSH_ENABLED -> pushOptInGranted
            Precondition.PUSH_DISABLED -> !pushOptInGranted
        }
    }

    override fun getActions(): Flow<Action> {
        return behaviorQueue.actionsFlow
    }

    override fun setPushPermissionDelegate(delegate: PushPermissionDelegate?) {
        pushPermissionProvider?.setDelegate(delegate)
    }
}
