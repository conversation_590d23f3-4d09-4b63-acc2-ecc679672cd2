package com.ottogroup.appkit.coordinator.platform

import com.ottogroup.appkit.coordinator.PushPermissionDelegate

/**
 * iOS-specific provider for push notification permission status using delegates.
 * Only exists on iOS - Android uses the configured value directly.
 */
internal expect class PushPermissionProvider(delegate: PushPermissionDelegate) {
    /**
     * Gets the current push notification permission status.
     * Uses the injected delegate.
     */
    suspend fun getPushOptInStatus(configuredValue: Boolean): <PERSON><PERSON><PERSON>
}


