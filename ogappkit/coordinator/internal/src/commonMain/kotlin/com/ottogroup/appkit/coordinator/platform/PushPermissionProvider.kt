package com.ottogroup.appkit.coordinator.platform

import com.ottogroup.appkit.coordinator.PushPermissionDelegate

/**
 * Provider for push notification permission status using delegates.
 * Simple wrapper around the delegate - works on all platforms.
 */
internal class PushPermissionProvider(private val delegate: PushPermissionDelegate) {
    /**
     * Gets the current push notification permission status.
     * Uses the injected delegate.
     */
    suspend fun getPushOptInStatus(configuredValue: Boolean): <PERSON><PERSON><PERSON> {
        return delegate.isPushOptInGranted()
    }
}
