package com.ottogroup.appkit.coordinator.platform

import com.ottogroup.appkit.coordinator.PushPermissionDelegate

/**
 * Platform-specific provider for push notification permission status.
 * - Android: Uses the configured value directly (simple and fast)
 * - iOS: Can use a delegate for automatic permission detection
 */
internal expect class PushPermissionProvider {
    /**
     * Sets the delegate for push permission checking (iOS only).
     * On Android, this is ignored and the configured value is always used.
     */
    fun setDelegate(delegate: PushPermissionDelegate?)

    /**
     * Gets the current push notification permission status.
     * - Android: Returns configuredValue directly
     * - iOS: Uses delegate if set, otherwise returns configuredValue
     */
    suspend fun getPushOptInStatus(configuredValue: Boolean): Boolean
}
