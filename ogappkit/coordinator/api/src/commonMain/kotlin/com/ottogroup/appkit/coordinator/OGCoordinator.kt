package com.ottogroup.appkit.coordinator

import com.ottogroup.appkit.coordinator.config.CoordinatorConfig
import com.ottogroup.appkit.coordinator.model.Action
import com.ottogroup.appkit.coordinator.model.CoordinatorEvent
import kotlinx.coroutines.flow.Flow

/**
 * The main entry point to Coordinator functionality. Obtain an instance from
 * the `OGAppKitSdk` object.
 */
public interface OGCoordinator {
    /**
     * Configures the Coordinator SDK. MUST be called before performing any other
     * operations.
     */
    public fun configure(config: CoordinatorConfig)

    /**
     * Sends a [CoordinatorEvent] to the Coordinator for processing.
     */
    public fun onEvent(event: CoordinatorEvent)

    /**
     * Gets a flow with the [Action] resulting from the events sent to the Coordinator.
     */
    public fun getActions(): Flow<Action>
}
