import Combine
import Foundation
import OGCore
import UIKit.UIApplication
import UserNotifications

// MARK: - OGUserNotificationCenterHandling

public protocol OGUserNotificationCenterHandling {
  func authorizationStatus() async -> UNAuthorizationStatus
  func requestAuthorization(options: UNAuthorizationOptions) async -> Bool
  var isPushOptInGranted: CurrentValueSubject<Bool, Never> { get }
}

// MARK: - OGUserNotificationCenter

public struct OGUserNotificationCenter: OGUserNotificationCenterHandling {
  private let getAuthorizationStatus: () async -> UNAuthorizationStatus
  private let _requestAuthorization: (UNAuthorizationOptions) async -> Bool
  public private(set) var isPushOptInGranted: CurrentValueSubject<Bool, Never> = .init(false)
  private var cancellables = Set<AnyCancellable>()
  public init(
    getAuthorizationStatus: @escaping () async -> UNAuthorizationStatus = UNUserNotificationCenter.current().authorizationStatus,
    requestAuthorization: @escaping (UNAuthorizationOptions) async -> Bool = UNUserNotificationCenter.current().requestAuthorization(options:)
  ) {
    self.getAuthorizationStatus = getAuthorizationStatus
    self._requestAuthorization = requestAuthorization

    Publishers.CombineLatest(
      NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification),
      NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)
    ).sink { [self] _, _ in
      Task {
        let _ = await authorizationStatus()
      }
    }.store(in: &cancellables)
  }

  public func authorizationStatus() async -> UNAuthorizationStatus {
    let status = await getAuthorizationStatus()
    isPushOptInGranted.send(status == .authorized)
    return status
  }

  public func requestAuthorization(options: UNAuthorizationOptions) async -> Bool {
    let granted = await _requestAuthorization(options)
    isPushOptInGranted.send(granted)
    return granted
  }
}

// MARK: - UNUserNotificationCenter + OGUserNotificationCenter

extension UNUserNotificationCenter {
  public func requestAuthorization(options: UNAuthorizationOptions) async -> Bool {
    let logger: OGLoggable = OGCoreContainer.shared.logger()

    return await withUnsafeContinuation { continuation in
      requestAuthorization(options: options) { success, error in
        if let error {
          logger.log(.critical, domain: .service, message: "Error requesting push authorization: \(error)")
        }
        logger.log(.debug, domain: .service, message: "Result of requesting push authorization: \(success)")
        continuation.resume(returning: success)
      }
    }
  }

  public func authorizationStatus() async -> UNAuthorizationStatus {
    await notificationSettings().authorizationStatus
  }
}
