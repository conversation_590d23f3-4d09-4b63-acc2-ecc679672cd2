import OGApp<PERSON>itSDK
@testable import OGDialogCoordinator
import OGDialogCoordinatorTestsUtils
import XCTest

final class OGDialogCoordinatorReducerTests: XCTestCase {
  func test_GIVEN_initialState_WHEN_update_THEN_stateUnchanged() {
    var state = OGDialogCoordinatorState.initial
    let originalState = state
    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._update(
        OGAppKitSDK.CoordinatorConfig(
          debounceMs: 1_000,
          behaviors: [DialogBehavior].stubs.map { $0.convertDialogBehaviorToCoordinatorBehavior()! },
          pushOptInGranted: true
        )
      )
    )
    XCTAssertEqual(originalState, state)
  }

  func test_GIVEN_state_WHEN_navigate_THEN_stateUnchanged() {
    var state = OGDialogCoordinatorState.stub
    let originalState = state
    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._navigate("")
    )
    XCTAssertEqual(originalState, state)
  }

  func test_GIVEN_state_WHEN_receivedScreenView_THEN_stateUnchanged() {
    var state = OGDialogCoordinatorState.stub
    let originalState = state
    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._receivedScreenView(.stub)
    )
    XCTAssertEqual(originalState, state)
  }

  func test_GIVEN_state_WHEN_receivedWebBridge_THEN_stateUnchanged() {
    var state = OGDialogCoordinatorState.stub
    let originalState = state
    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._receivedWebBridge(.stub)
    )
    XCTAssertEqual(originalState, state)
  }

  func test_GIVEN_initialState_WHEN_updateWithEmptyBehaviors_THEN_stateUnchanged() {
    var state = OGDialogCoordinatorState.initial
    let originalState = state
    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._update(
        OGAppKitSDK.CoordinatorConfig(
          debounceMs: 500,
          behaviors: [],
          pushOptInGranted: false
        )
      )
    )
    XCTAssertEqual(originalState, state)
  }

  func test_GIVEN_initialState_WHEN_updateWithZeroDebounce_THEN_stateUnchanged() {
    var state = OGDialogCoordinatorState.initial
    let originalState = state
    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._update(
        OGAppKitSDK.CoordinatorConfig(
          debounceMs: 0,
          behaviors: [DialogBehavior].stubs.map { $0.convertDialogBehaviorToCoordinatorBehavior()! },
          pushOptInGranted: true
        )
      )
    )
    XCTAssertEqual(originalState, state)
  }

  func test_GIVEN_state_WHEN_navigateWithEmptyURL_THEN_stateUnchanged() {
    var state = OGDialogCoordinatorState.stub
    let originalState = state
    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._navigate("")
    )
    XCTAssertEqual(originalState, state)
  }

  func test_GIVEN_state_WHEN_navigateWithLongURL_THEN_stateUnchanged() {
    var state = OGDialogCoordinatorState.stub
    let originalState = state
    let longURL = "https://example.com/" + String(repeating: "path/", count: 100)
    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._navigate(longURL)
    )
    XCTAssertEqual(originalState, state)
  }

  func test_GIVEN_state_WHEN_multipleActionsInSequence_THEN_stateHandledCorrectly() {
    var state = OGDialogCoordinatorState.initial
    let originalState = state

    // Apply multiple actions in sequence
    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._update(.stub)
    )

    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._receivedScreenView(.stub)
    )

    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._navigate("test://url")
    )

    // State should remain unchanged for this implementation
    XCTAssertEqual(originalState, state)
  }
}
