import Combine
import OGCore
import OGCoreTestsUtils
import OGDialogCoordinatorTestsUtils
import OGFeatureAdapter
import OGMock
import OGScreenViewUpdate
import OGStorage
import OGSystemKit
import OGWebBridge
import OGWebBridgeTestsUtils
import XCTest

@testable import OGDialogCoordinator

final class OGDialogCoordinatorTests: XCTestCase {
  private var webBridgeMock: WebBridgeMock!
  private var webBridgeHandlerMock: OGDialogCoordinatorWebBridgeActionHandlerMock!
  private var sut: OGDialogCoordinator!

  override func setUpWithError() throws {
    try super.setUpWithError()

    webBridgeMock = WebBridgeMock()

    OGWebBridgeContainer.shared.globalWebBridge.register { self.webBridgeMock }

    webBridgeHandlerMock = OGDialogCoordinatorWebBridgeActionHandlerMock()
    OGDialogCoordinatorContainer.shared.webBridgeActionHandler.register { self.webBridgeHandlerMock }

    sut = OGDialogCoordinator()
  }

  override func tearDownWithError() throws {
    OGWebBridgeContainer.shared.globalWebBridge.reset()
    OGDialogCoordinatorContainer.shared.webBridgeActionHandler.reset()
    webBridgeMock = nil
    webBridgeHandlerMock = nil
    sut = nil

    try super.tearDownWithError()
  }

  func test_WHEN_init_THEN_webBridgeHandlerAdded() {
    XCTAssertEqual(webBridgeMock.mock.addActionHandlerCalls.callsCount, 1)
    XCTAssertEqual(
      webBridgeMock.mock.addActionHandlerCalls.latestCall?.webBridgeNames,
      webBridgeHandlerMock.webBridgeNames
    )
  }

  func test_WHEN_addWebBridgeNames_THEN_handlerUpdated() {
    let names = ["testName1", "testName2"]
    sut.addWebBridgeNames(names: names)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.latestCall, names)
  }

  func test_createCoordinatorConfig_withPushOptInGranted() {
    let featureConfig = OGDialogCoordinatorFeatureConfig(isEnabled: true, behaviors: [.stubPush, .stubRating], debounceMs: 500)
    let config = sut.createCoordinatorConfig(from: featureConfig, pushOptInGranted: true)

    XCTAssertEqual(config.debounceMs, 500)
    XCTAssertEqual(config.pushOptInGranted, true)
    XCTAssertEqual(config.behaviors.count, 2)
  }

  func test_createCoordinatorConfig_withPushOptInDenied() {
    let featureConfig = OGDialogCoordinatorFeatureConfig(isEnabled: true, behaviors: [.stubPush, .stubRating], debounceMs: 1_000)
    let config = sut.createCoordinatorConfig(from: featureConfig, pushOptInGranted: false)

    XCTAssertEqual(config.debounceMs, 1_000)
    XCTAssertEqual(config.pushOptInGranted, false)
    XCTAssertEqual(config.behaviors.count, 2)
  }

  func test_createCoordinatorConfig_withEmptyBehaviors() {
    let featureConfig = OGDialogCoordinatorFeatureConfig(isEnabled: true, behaviors: [], debounceMs: 250)
    let config = sut.createCoordinatorConfig(from: featureConfig, pushOptInGranted: true)

    XCTAssertEqual(config.debounceMs, 250)
    XCTAssertEqual(config.pushOptInGranted, true)
    XCTAssertEqual(config.behaviors.count, 0)
  }

  func test_createCoordinatorConfig_withSingleBehavior() {
    let featureConfig = OGDialogCoordinatorFeatureConfig(isEnabled: true, behaviors: [.stubPush], debounceMs: 750)
    let config = sut.createCoordinatorConfig(from: featureConfig, pushOptInGranted: false)

    XCTAssertEqual(config.debounceMs, 750)
    XCTAssertEqual(config.pushOptInGranted, false)
    XCTAssertEqual(config.behaviors.count, 1)
  }

  func test_createCoordinatorConfig_withZeroDebounce() {
    let featureConfig = OGDialogCoordinatorFeatureConfig(isEnabled: true, behaviors: [.stubRating], debounceMs: 0)
    let config = sut.createCoordinatorConfig(from: featureConfig, pushOptInGranted: true)

    XCTAssertEqual(config.debounceMs, 0)
    XCTAssertEqual(config.pushOptInGranted, true)
    XCTAssertEqual(config.behaviors.count, 1)
  }

  func test_WHEN_addWebBridgeNames_withEmptyArray_THEN_handlerUpdated() {
    let names: [String] = []
    sut.addWebBridgeNames(names: names)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.latestCall, names)
  }

  func test_WHEN_addWebBridgeNames_withMultipleNames_THEN_handlerUpdated() {
    let names = ["name1", "name2", "name3", "name4"]
    sut.addWebBridgeNames(names: names)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.latestCall, names)
  }

  func test_WHEN_addWebBridgeNames_calledMultipleTimes_THEN_handlerUpdatedEachTime() {
    let firstNames = ["first"]
    let secondNames = ["second", "third"]

    sut.addWebBridgeNames(names: firstNames)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.callsCount, 1)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.latestCall, firstNames)

    sut.addWebBridgeNames(names: secondNames)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.callsCount, 2)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.latestCall, secondNames)
  }

  func test_createCoordinatorConfig_withDisabledFeature() {
    let featureConfig = OGDialogCoordinatorFeatureConfig(isEnabled: false, behaviors: [.stubPush], debounceMs: 500)
    let config = sut.createCoordinatorConfig(from: featureConfig, pushOptInGranted: true)

    XCTAssertEqual(config.debounceMs, 500)
    XCTAssertEqual(config.pushOptInGranted, true)
    XCTAssertEqual(config.behaviors.count, 1) // behaviors are still included even if disabled
  }

  func test_createCoordinatorConfig_withLargeDebounceValue() {
    let featureConfig = OGDialogCoordinatorFeatureConfig(isEnabled: true, behaviors: [.stubPush], debounceMs: Int.max)
    let config = sut.createCoordinatorConfig(from: featureConfig, pushOptInGranted: false)

    XCTAssertEqual(config.debounceMs, Int64(Int.max))
    XCTAssertEqual(config.pushOptInGranted, false)
    XCTAssertEqual(config.behaviors.count, 1)
  }

  func test_createCoordinatorConfig_withNegativeDebounceValue() {
    let featureConfig = OGDialogCoordinatorFeatureConfig(isEnabled: true, behaviors: [.stubRating], debounceMs: -100)
    let config = sut.createCoordinatorConfig(from: featureConfig, pushOptInGranted: true)

    XCTAssertEqual(config.debounceMs, -100)
    XCTAssertEqual(config.pushOptInGranted, true)
    XCTAssertEqual(config.behaviors.count, 1)
  }

  func test_createCoordinatorConfig_withBehaviorsContainingNilConversion() {
    // Test with behaviors that might fail conversion to coordinator behavior
    let behaviorWithComplexConditions = DialogBehavior(
      id: "complex",
      maxInvocations: nil,
      preCondition: .none,
      conditions: [DialogCondition.stubTracking], // This might not convert properly
      action: .stubReview,
      disabledUrls: [],
      minutesBetweenInvocations: nil
    )

    let featureConfig = OGDialogCoordinatorFeatureConfig(
      isEnabled: true,
      behaviors: [.stubPush, behaviorWithComplexConditions],
      debounceMs: 300
    )
    let config = sut.createCoordinatorConfig(from: featureConfig, pushOptInGranted: true)

    XCTAssertEqual(config.debounceMs, 300)
    XCTAssertEqual(config.pushOptInGranted, true)
    // The count might be less than 2 if behaviorWithComplexConditions fails conversion
    XCTAssertGreaterThanOrEqual(config.behaviors.count, 1)
    XCTAssertLessThanOrEqual(config.behaviors.count, 2)
  }

  func test_WHEN_addWebBridgeNames_withDuplicateNames_THEN_handlerUpdated() {
    let names = ["duplicate", "duplicate", "unique"]
    sut.addWebBridgeNames(names: names)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.latestCall, names)
  }

  func test_WHEN_addWebBridgeNames_withVeryLongNames_THEN_handlerUpdated() {
    let longName = String(repeating: "a", count: 1_000)
    let names = [longName, "short"]
    sut.addWebBridgeNames(names: names)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.latestCall, names)
  }
}
