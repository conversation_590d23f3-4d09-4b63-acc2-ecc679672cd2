import OGApp<PERSON>itSDK
import <PERSON><PERSON>ore
@testable import OGDialogCoordinator
import OGDialogCoordinatorTestsUtils
import OGDIService
import OGMock
import OGRouter
import OGRouterTestsUtils
import XCTest

final class OGDialogCoordinatorMiddlewareTests: XCTestCase {
  var coordinatorMock: OGAppKitCoordinatorMock!
  var routePublisherMock: OGRoutePublisherMock!

  override func setUpWithError() throws {
    let coordinatorMock = OGAppKitCoordinatorMock()
    OGCoreContainer.shared.coordinator.register {
      coordinatorMock
    }
    self.coordinatorMock = coordinatorMock

    let routePublisherMock = OGRoutePublisherMock()
    OGRoutingContainer.shared.routePublisher.register {
      routePublisherMock
    }
    self.routePublisherMock = routePublisherMock
    try super.setUpWithError()
  }

  override func tearDownWithError() throws {
    OGRoutingContainer.shared.routePublisher.reset()
    OGCoreContainer.shared.coordinator.reset()
    coordinatorMock = nil
    routePublisherMock = nil
    try super.tearDownWithError()
  }

  func test_GIVEN_initialState_WHEN_update_THEN_coordinatorConfigured() async throws {
    let sut = OGDialogCoordinatorMiddleware()
    let debounceMs: Int64 = 100
    let behaviorsJson = "[]"
    let pushOptInGranted = true

    let nextAction = try await sut.callAsFunction(
      action: OGDialogCoordinatorAction._update(debounceMs: debounceMs, behaviorsJson: behaviorsJson, pushOptInGranted: pushOptInGranted),
      for: .initial
    )

    XCTAssertNil(nextAction)
    XCTAssertEqual(coordinatorMock.mock.configureCalls.callsCount, 1)
    let call = coordinatorMock.mock.configureCalls.latestCall
    XCTAssertEqual(call?.debounceMs, debounceMs)
    XCTAssertEqual(call?.behaviorsJson, behaviorsJson)
    XCTAssertEqual(call?.pushOptInGranted, pushOptInGranted)
  }

  func test_GIVEN_state_WHEN_receivedWebBridge_THEN_coordinatorEventSent() async throws {
    let sut = OGDialogCoordinatorMiddleware()
    let webBridgeCallName = "testWebBridgeCall"

    let nextAction = try await sut.callAsFunction(
      action: OGDialogCoordinatorAction._receivedWebBridge(webBridgeCallName),
      for: .stub
    )

    XCTAssertNil(nextAction)
    XCTAssertEqual(coordinatorMock.mock.onEventCalls.callsCount, 1)

    let event = coordinatorMock.mock.onEventCalls.latestCall as? OGAppKitSDK.CoordinatorEventWebBridgeCall
    XCTAssertNotNil(event)
    XCTAssertEqual(event?.name, webBridgeCallName)
  }

  func test_GIVEN_state_WHEN_receivedScreenView_THEN_coordinatorEventSent() async throws {
    let sut = OGDialogCoordinatorMiddleware()
    let testURL = URL(string: "https://example.com")!

    let nextAction = try await sut.callAsFunction(
      action: OGDialogCoordinatorAction._receivedScreenView(testURL),
      for: .stub
    )

    XCTAssertNil(nextAction)
    XCTAssertEqual(coordinatorMock.mock.onEventCalls.callsCount, 1)

    let event = coordinatorMock.mock.onEventCalls.latestCall as? OGAppKitSDK.CoordinatorEventScreenView
    XCTAssertNotNil(event)
    XCTAssertEqual(event?.url, testURL.absoluteString)
  }

  func test_GIVEN_state_WHEN_navigate_THEN_routePublished() async throws {
    let sut = OGDialogCoordinatorMiddleware()
    let testURL = "https://example.com"

    let nextAction = try await sut.callAsFunction(
      action: OGDialogCoordinatorAction._navigate(testURL),
      for: .stub
    )

    XCTAssertNil(nextAction)
    XCTAssertEqual(routePublisherMock.mock.sendCalls.callsCount, 1)
    XCTAssertEqual(routePublisherMock.mock.sendCalls.latestCall, OGRoute(testURL))
  }

  func test_GIVEN_state_WHEN_updateWithEmptyBehaviors_THEN_coordinatorConfigured() async throws {
    let sut = OGDialogCoordinatorMiddleware()
    let debounceMs: Int64 = 0
    let behaviorsJson = "[]"
    let pushOptInGranted = false

    let nextAction = try await sut.callAsFunction(
      action: OGDialogCoordinatorAction._update(debounceMs: debounceMs, behaviorsJson: behaviorsJson, pushOptInGranted: pushOptInGranted),
      for: .stub
    )

    XCTAssertNil(nextAction)
    XCTAssertEqual(coordinatorMock.mock.configureCalls.callsCount, 1)
    let call = coordinatorMock.mock.configureCalls.latestCall
    XCTAssertEqual(call?.debounceMs, debounceMs)
    XCTAssertEqual(call?.behaviorsJson, behaviorsJson)
    XCTAssertEqual(call?.pushOptInGranted, pushOptInGranted)
  }

  func test_GIVEN_state_WHEN_receivedWebBridgeWithEmptyName_THEN_coordinatorEventSent() async throws {
    let sut = OGDialogCoordinatorMiddleware()
    let webBridgeCallName = ""

    let nextAction = try await sut.callAsFunction(
      action: OGDialogCoordinatorAction._receivedWebBridge(webBridgeCallName),
      for: .stub
    )

    XCTAssertNil(nextAction)
    XCTAssertEqual(coordinatorMock.mock.onEventCalls.callsCount, 1)

    let event = coordinatorMock.mock.onEventCalls.latestCall as? OGAppKitSDK.CoordinatorEventWebBridgeCall
    XCTAssertNotNil(event)
    XCTAssertEqual(event?.name, webBridgeCallName)
  }

  func test_GIVEN_state_WHEN_navigateWithEmptyURL_THEN_routePublished() async throws {
    let sut = OGDialogCoordinatorMiddleware()
    let testURL = ""

    let nextAction = try await sut.callAsFunction(
      action: OGDialogCoordinatorAction._navigate(testURL),
      for: .stub
    )

    XCTAssertNil(nextAction)
    XCTAssertEqual(routePublisherMock.mock.sendCalls.callsCount, 1)
    XCTAssertEqual(routePublisherMock.mock.sendCalls.latestCall, OGRoute(testURL))
  }

  func test_GIVEN_state_WHEN_receivedScreenViewWithComplexURL_THEN_coordinatorEventSent() async throws {
    let sut = OGDialogCoordinatorMiddleware()
    let complexURL = URL(string: "https://example.com/path?param1=value1&param2=value2#fragment")!

    let nextAction = try await sut.callAsFunction(
      action: OGDialogCoordinatorAction._receivedScreenView(complexURL),
      for: .stub
    )

    XCTAssertNil(nextAction)
    XCTAssertEqual(coordinatorMock.mock.onEventCalls.callsCount, 1)

    let event = coordinatorMock.mock.onEventCalls.latestCall as? OGAppKitSDK.CoordinatorEventScreenView
    XCTAssertNotNil(event)
    XCTAssertEqual(event?.url, complexURL.absoluteString)
  }
}
