import Foundation
import OGAppKitSDK
import OGCoreTestsUtils
import OGDialogCoordinator

extension OGDialogCoordinatorFeatureConfig {
  public static let stub = OGDialogCoordinatorFeatureConfig(
    isEnabled: true,
    behaviors: .stubs,
    debounceMs: 500
  )
}

extension OGDialogCoordinatorState {
  public static let stub = OGDialogCoordinatorState(
    isAwaitingUpdate: false
  )
  public static func stub(willStartNavigateAt _: TimeInterval) -> OGDialogCoordinatorState {
    OGDialogCoordinatorState(
      isAwaitingUpdate: false
    )
  }

  public static func stub(
    willStartNavigateAt _: TimeInterval,
    didStartNavigateAt _: TimeInterval
  )
    -> OGDialogCoordinatorState {
    OGDialogCoordinatorState(
      isAwaitingUpdate: false
    )
  }
}

extension CoordinatorConfig {
  public static let stub = CoordinatorConfig(
    debounceMs: 100,
    behaviors:
    [DialogBehavior].stubs.map { $0.convertDialogBehaviorToCoordinatorBehavior()! },

    pushOptInGranted: true
  )
}

extension DialogBehavior {
  public static let stubPush = DialogBehavior(
    id: "push",
    maxInvocations: 1,
    preCondition: .pushDisabled,
    conditions: DialogCondition.stubs,
    action: .stubPush,
    disabledUrls: ["app://account"],
    minutesBetweenInvocations: 1
  )

  public static let stubRating = DialogBehavior(
    id: "rating",
    maxInvocations: 1,
    preCondition: .none,
    conditions: [DialogCondition.stubWebBridgeCall],
    action: .stubRating,
    disabledUrls: ["app://rating"],
    minutesBetweenInvocations: 1
  )

  public static let stubReviewFirstStart = DialogBehavior(
    id: "review",
    maxInvocations: 1,
    preCondition: .none,
    conditions: [DialogCondition.stubAppStarts],
    action: .stubReview,
    disabledUrls: ["app://account"],
    minutesBetweenInvocations: nil
  )

  public static let stubReviewSecondStart = DialogBehavior(
    id: "review",
    maxInvocations: 1,
    preCondition: .none,
    conditions: [DialogCondition.stubAppStartsTwo],
    action: .stubReview,
    disabledUrls: ["app://account"],
    minutesBetweenInvocations: nil
  )
}

extension [DialogBehavior] {
  public static let stubs = [
    Element.stubPush, Element.stubRating,
    Element.stubReviewFirstStart, Element.stubReviewSecondStart
  ]
}

extension DialogCondition {
  public static let stubAppStarts = DialogCondition(
    type: .appStarts,
    start: 1
  )

  public static let stubAppStartsTwo = DialogCondition(
    type: .appStarts,
    start: 2
  )

  public static let stubScreenViews = DialogCondition(
    type: .screenViews,
    period: 2,
  )

  public static let stubWebBridgeCall = DialogCondition(
    type: .webBridgeCall,
    webBridgeCallName: "webBridgeCallName"
  )

  public static let stubTracking = DialogCondition(
    type: .trackingEvent,
    eventName: "eventName",
    countType: .session
  )

  public static let stubs = [stubAppStarts, stubScreenViews, stubWebBridgeCall]
}

extension DialogAction {
  public static let stubPush = DialogAction(type: .navigation, url: "app://push")
  public static let stubRating = DialogAction(type: .navigation, url: "app://rating")
  public static let stubReview = DialogAction(type: .navigation, url: "app://review")
}
