import Foundation
public struct DialogCondition: Codable, Equatable, Sendable {
  public enum ConditionType: String, Codable, Sendable {
    case appStarts
    case screenViews
    case webBridgeCall
    case trackingEvent
    case none
  }

  public enum CountType: String, Codable, Sendable {
    case session
    case total
    case none
  }

  let type: ConditionType
  let start: Int?
  let period: Int?
  let webBridgeCallName: String?
  let eventName: String?
  let countType: CountType?

  public init(
    type: ConditionType,

    start: Int? = nil,
    period: Int? = nil,
    webBridgeCallName: String? = nil,
    eventName: String? = nil,
    countType: CountType? = nil
  ) {
    self.type = type
    self.start = start
    self.period = period
    self.webBridgeCallName = webBridgeCallName
    self.eventName = eventName
    self.countType = countType
  }

  public init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    self.webBridgeCallName = try container.decodeIfPresent(String.self, forKey: .webBridgeCallName)
    self.eventName = try container.decodeIfPresent(String.self, forKey: .eventName)
    self.start = try container.decodeIfPresent(Int.self, forKey: .start)
    self.period = try container.decodeIfPresent(Int.self, forKey: .period)
    let type = try container.decodeIfPresent(String.self, forKey: .type)
    switch type {
    case .none:
      self.type = .none
    case let .some(value):
      self.type = ConditionType(rawValue: value) ?? .none
    }
    let countType = try container.decodeIfPresent(String.self, forKey: .countType)
    switch countType {
    case .none:
      self.countType = DialogCondition.CountType.none
    case let .some(value):
      self.countType = CountType(rawValue: value) ?? DialogCondition.CountType.none
    }
  }
}
