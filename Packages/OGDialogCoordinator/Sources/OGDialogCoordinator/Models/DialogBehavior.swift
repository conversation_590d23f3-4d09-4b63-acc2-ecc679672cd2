import Foundation

// MARK: - DialogBehavior

public struct DialogBehavior: Codable, Equatable, Sendable {
  public enum PreCondition: String, Codable, Sendable {
    case pushDisabled
    case none
  }

  let id: String
  let maxInvocations: Int?
  let minutesBetweenInvocations: Int?
  let precondition: PreCondition
  let conditions: [DialogCondition]
  let disabledUrls: [String]
  let action: DialogAction

  public init(
    id: String,
    maxInvocations: Int?,
    preCondition: PreCondition,
    conditions: [DialogCondition],
    action: DialogAction,
    disabledUrls: [String],
    minutesBetweenInvocations: Int?
  ) {
    self.id = id
    self.maxInvocations = maxInvocations
    self.precondition = preCondition
    self.conditions = conditions
    self.action = action
    self.disabledUrls = disabledUrls
    self.minutesBetweenInvocations = minutesBetweenInvocations
  }

  public init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    self.id = try container.decode(String.self, forKey: .id)
    self.maxInvocations = try container.decodeIfPresent(Int.self, forKey: .maxInvocations)
    self.minutesBetweenInvocations = try container.decodeIfPresent(Int.self, forKey: .minutesBetweenInvocations)
    self.conditions = try container.decodeIfPresent([DialogCondition].self, forKey: .conditions) ?? []
    self.action = try container.decode(DialogAction.self, forKey: .action)
    self.disabledUrls = try container.decodeIfPresent([String].self, forKey: .disabledUrls) ?? []
    let precondition = try container.decodeIfPresent(String.self, forKey: .precondition)
    switch precondition {
    case .none:
      self.precondition = .none
    case let .some(value):
      self.precondition = PreCondition(rawValue: value) ?? .none
    }
  }
}

import OGAppKitSDK

extension DialogBehavior {
  public func convertDialogBehaviorToCoordinatorBehavior() -> OGAppKitSDK.Behavior? {
    let conditions = conditions.compactMap { condition -> OGAppKitSDK.Condition? in
      switch condition.type {
      case .appStarts:
        return OGAppKitSDK.ConditionAppStarts(
          start: Int32(condition.start ?? 0),
          period: Int32(condition.period ?? 0)
        )
      case .screenViews:
        return OGAppKitSDK.ConditionScreenViews(
          start: Int32(condition.start ?? 0),
          period: Int32(condition.period ?? 0)
        )
      case .webBridgeCall:
        return OGAppKitSDK.ConditionWebBridgeCall(webBridgeCallName: condition.webBridgeCallName ?? "")
      case .trackingEvent where condition.countType == .session:
        return OGAppKitSDK.ConditionTrackingEvents(
          eventName: condition.eventName ?? "",
          countType: .session,
          start: Int32(condition.start ?? 0),
          period: Int32(condition.period ?? 0)
        )
      case .trackingEvent where condition.countType == .total:
        return OGAppKitSDK.ConditionTrackingEvents(
          eventName: condition.eventName ?? "",
          countType: .total,
          start: Int32(condition.start ?? 0),
          period: Int32(condition.period ?? 0)
        )
      case .none, .trackingEvent:
        return nil
      }
    }

    let action: OGAppKitSDK.Action
    switch self.action.type {
    case .navigation:
      action = OGAppKitSDK.ActionNavigation(url: self.action.url)
    default:
      action = OGAppKitSDK.ActionNavigation(url: self.action.url)
    }

    let precondition: OGAppKitSDK.Precondition?
    switch self.precondition {
    case .pushDisabled:
      precondition = OGAppKitSDK.Precondition.pushDisabled
    case .none:
      precondition = nil
    }

    return OGAppKitSDK.Behavior(
      precondition: precondition,
      conditions: conditions,
      action: action,
      id: id,
      maxInvocations: maxInvocations?.toKotlinInt(),
      disabledUrls: disabledUrls.map { OGAppKitSDK.UrlMatcher(pattern: $0) },
      minutesBetweenInvocations: minutesBetweenInvocations?.toKotlinInt()
    )
  }
}
