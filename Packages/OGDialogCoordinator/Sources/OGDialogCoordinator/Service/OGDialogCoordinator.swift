import Combine
import Foundation
@preconcurrency import OGAppKitSDK
import OGCore
import OGDIService
import OGScreenViewUpdate
import OGSystemKit
import OGWebBridge
import UIKit
import UserNotifications

// MARK: - OGDialogCoordinable

public protocol OGDialogCoordinable {
  var webBridgeActionHandler: OGDialogCoordinatorWebBridgeActionHandlable { get async }
  func addWebBridgeNames(names: [String])
}

// MARK: - OGDialogCoordinator

final class OGDialogCoordinator: OGDialogCoordinable {
  @OGInjected(\OGDialogCoordinatorContainer.webBridgeActionHandler) private var _webBridgeActionHandler
  @OGInjected(\OGWebBridgeContainer.globalWebBridge) private var globalWebBridge
  @OGInjected(\OGCoreContainer.logger) private var logger

  var webBridgeActionHandler: OGDialogCoordinatorWebBridgeActionHandlable {
    get async { _webBridgeActionHandler }
  }

  init() {
    globalWebBridge.addActionHandler(_webBridgeActionHandler)
  }

  func addWebBridgeNames(names: [String]) {
    _webBridgeActionHandler.update(webBridgeCallName: names)
  }


}





extension Int {
  func toKotlinInt() -> KotlinInt {
    KotlinInt(integerLiteral: self)
  }
}
